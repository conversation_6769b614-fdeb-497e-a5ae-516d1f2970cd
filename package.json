{"name": "nextjs-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:https": "next dev --experimental-https", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@telegram-apps/sdk-react": "^3.2.4", "@telegram-apps/telegram-ui": "^2.1.5", "@tonconnect/ui-react": "^2.1.0", "eruda": "^3.0.1", "next": "15.3.1", "next-intl": "^4.1.0", "normalize.css": "^8.0.1", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}