"use client";

import { type PropsWithChildren, useEffect, useState } from "react";

import { TelegramErrorBoundary } from "@/components/TelegramErrorBoundary";
import { init } from "@/core/init";

function RootInner({ children }: PropsWithChildren) {
  return <>{children}</>;
}

export function Root(props: PropsWithChildren) {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      init({
        debug: process.env.NODE_ENV === "development",
        eruda: false,
        mockForMacOS: false,
      })
        .then(() => {
          console.log("✅ Telegram SDK initialized");
          setIsInitialized(true);
        })
        .catch((e) => {
          console.log("Telegram SDK initialization error:", e);
          setIsInitialized(true);
        });
    }
  }, []);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing Telegram Mini App...</p>
        </div>
      </div>
    );
  }

  return (
    <TelegramErrorBoundary>
      <RootInner {...props} />
    </TelegramErrorBoundary>
  );
}
