"use client";

import TelegramAuth from "@/components/telegram-auth";
import { useDidMount } from "@/hooks/useDidMount";

export default function TelegramAuthPage() {
  const didMount = useDidMount();

  if (!didMount) {
    return null;
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-12 p-6 border rounded-lg bg-gradient-to-r from-blue-50 to-cyan-50">
          <TelegramAuth
            onSuccess={() => {
              window.location.href = "/profile";
            }}
            onError={(error) => {
              alert(`Authentication failed: ${error}`);
            }}
          />
        </div>

        <div className="mb-8">{/* <TelegramTest /> */}</div>
      </div>
    </div>
  );
}
